// Simple Client Script for Salary Slip Report
// Copy this code into the Client Script section of your Script Report in Frappe Desk

frappe.query_reports["Report Test"] = {
    "filters": [
        {
            "fieldname": "company",
            "label": __("Company"),
            "fieldtype": "Link",
            "options": "Company",
            "default": frappe.defaults.get_user_default("Company")
        },
        {
            "fieldname": "from_date",
            "label": __("From Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.add_months(frappe.datetime.get_today(), -1)
        },
        {
            "fieldname": "to_date",
            "label": __("To Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.get_today()
        }
    ],
    
    "onload": function(report) {
        // Add Submit All Draft button
        report.page.add_inner_button(__("Submit All Draft"), function() {
            submit_all_draft_slips();
        });
        
        // Add Submit Selected button
        report.page.add_inner_button(__("Submit Selected"), function() {
            submit_selected_slips();
        });
        
        // Add selection functionality after report loads
        setTimeout(function() {
            add_checkboxes();
        }, 1000);
    },
    
    "formatter": function(value, row, column, data, default_formatter) {
        value = default_formatter(value, row, column, data);
        
        // Add checkbox to the first column (Salary Slip name)
        if (column.fieldname == "name" && data && data.name) {
            value = `<input type="checkbox" class="slip-checkbox" data-name="${data.name}" style="margin-right: 8px;">${value}`;
        }
        
        return value;
    }
};

// Add select all functionality
function add_checkboxes() {
    if (!$('.select-all-header').length) {
        $('.dt-scrollable').before(`
            <div class="select-all-header" style="padding: 10px; background: #f8f9fa; border-bottom: 1px solid #dee2e6;">
                <label style="margin: 0;">
                    <input type="checkbox" class="select-all" style="margin-right: 8px;">
                    Select All Salary Slips
                </label>
            </div>
        `);
        
        // Handle select all
        $('.select-all').on('change', function() {
            $('.slip-checkbox').prop('checked', $(this).is(':checked'));
        });
    }
}

// Submit all draft salary slips
function submit_all_draft_slips() {
    frappe.confirm(
        __("Are you sure you want to submit all draft salary slips?"),
        function() {
            frappe.call({
                method: "frappe.client.get_list",
                args: {
                    doctype: "Salary Slip",
                    filters: { docstatus: 0 },
                    fields: ["name"]
                },
                callback: function(r) {
                    if (r.message && r.message.length > 0) {
                        let slip_names = r.message.map(d => d.name);
                        submit_slips(slip_names);
                    } else {
                        frappe.msgprint(__("No draft salary slips found"));
                    }
                }
            });
        }
    );
}

// Submit selected salary slips
function submit_selected_slips() {
    let selected = [];
    $('.slip-checkbox:checked').each(function() {
        selected.push($(this).data('name'));
    });
    
    if (selected.length === 0) {
        frappe.msgprint(__("Please select salary slips to submit"));
        return;
    }
    
    frappe.confirm(
        __("Are you sure you want to submit {0} salary slip(s)?", [selected.length]),
        function() {
            submit_slips(selected);
        }
    );
}

// Submit salary slips with error handling
function submit_slips(slip_names) {
    let success = 0;
    let failed = 0;
    let total = slip_names.length;
    let failed_names = [];

    frappe.show_progress(__("Submitting Salary Slips"), 0, total);

    slip_names.forEach(function(slip_name, index) {
        setTimeout(function() {
            frappe.call({
                method: "frappe.client.submit",
                args: {
                    doc: {
                        doctype: "Salary Slip",
                        name: slip_name
                    }
                },
                callback: function(r) {
                    if (r.exc) {
                        failed++;
                        failed_names.push(slip_name);
                    } else {
                        success++;
                    }
                    
                    frappe.show_progress(__("Submitting Salary Slips"), success + failed, total);
                    
                    if (success + failed === total) {
                        frappe.hide_progress();
                        
                        let msg = __("{0} salary slip(s) submitted successfully", [success]);
                        if (failed > 0) {
                            msg += __("<br>{0} failed to submit", [failed]);
                        }
                        
                        frappe.msgprint({
                            message: msg,
                            indicator: failed > 0 ? 'orange' : 'green'
                        });
                        
                        // Refresh report
                        if (window.cur_report_wrapper && window.cur_report_wrapper.report) {
                            window.cur_report_wrapper.report.refresh();
                        }
                    }
                },
                error: function() {
                    failed++;
                    failed_names.push(slip_name);
                    
                    frappe.show_progress(__("Submitting Salary Slips"), success + failed, total);
                    
                    if (success + failed === total) {
                        frappe.hide_progress();
                        
                        let msg = __("{0} salary slip(s) submitted successfully", [success]);
                        if (failed > 0) {
                            msg += __("<br>{0} failed to submit", [failed]);
                        }
                        
                        frappe.msgprint({
                            message: msg,
                            indicator: failed > 0 ? 'orange' : 'green'
                        });
                        
                        // Refresh report
                        if (window.cur_report_wrapper && window.cur_report_wrapper.report) {
                            window.cur_report_wrapper.report.refresh();
                        }
                    }
                }
            });
        }, index * 500); // 500ms delay between submissions
    });
}
