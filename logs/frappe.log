2025-07-20 13:37:50,801 ERROR frappe Could not take error snapshot: No module named 'payware'
Site: rubis
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'payware'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'payware'
2025-07-20 13:37:53,775 ERROR frappe Could not take error snapshot: No module named 'payware'
Site: rubis
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 258, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1670, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1518, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1483, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1513, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'payware'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1308, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/csf_tz/csf_tz/__init__.py", line 41, in get_hooks
    return old_get_hooks(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1619, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1588, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/frappe-bench/apps/frappe/frappe/__init__.py", line 1454, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1140, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'payware'
2025-07-20 14:02:44,612 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Report Test', 'filters': '{"company":"Rubis Technical Services Limited","from_date":"2025-06-20","to_date":"2025-07-20"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-20 14:03:17,138 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Report Test', 'filters': '{"company":"Rubis Technical Services Limited","from_date":"2025-06-20","to_date":"2025-07-20"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-20 14:08:14,191 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Report Test', 'filters': '{"company":"Rubis Technical Services Limited","from_date":"2025-06-20","to_date":"2025-07-20"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-20 14:08:15,641 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Report Test', 'filters': '{"company":"Rubis Technical Services Limited","from_date":"2025-06-20","to_date":"2025-07-20"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-20 14:08:21,414 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Report Test', 'filters': '{"company":"Rubis Technical Services Limited","from_date":"2025-06-20","to_date":"2025-07-20"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-20 14:09:40,158 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Report Test', 'filters': '{"company":"Rubis Technical Services Limited","from_date":"2025-06-20","to_date":"2025-07-20"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-20 15:06:10,391 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Report Test', 'filters': '{"company":"Rubis Technical Services Limited","from_date":"2025-07-01","to_date":"2025-07-31","status":"Draft"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-20 16:25:49,611 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'role': 'System Manager', 'permlevel': '0', 'doctype': 'Salary Slip', 'ptype': 'select', 'value': '1', 'if_owner': '0', 'cmd': 'frappe.core.page.permission_manager.permission_manager.update'}
2025-07-20 16:26:06,496 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'role': 'System Manager', 'permlevel': '0', 'doctype': 'Salary Slip', 'ptype': 'select', 'value': '1', 'if_owner': '0', 'cmd': 'frappe.core.page.permission_manager.permission_manager.update'}
2025-07-20 16:26:25,846 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'role': 'System Manager', 'permlevel': '0', 'doctype': 'Salary Slip', 'ptype': 'delete', 'value': '1', 'if_owner': '0', 'cmd': 'frappe.core.page.permission_manager.permission_manager.update'}
2025-07-20 16:26:56,803 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'role': 'HR Manager', 'permlevel': '0', 'doctype': 'Salary Slip', 'ptype': 'select', 'value': '1', 'if_owner': '0', 'cmd': 'frappe.core.page.permission_manager.permission_manager.update'}
2025-07-20 17:53:34,090 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'report_name': 'Report Test', 'filters': '{"company":"Rubis Technical Services Limited","from_date":"2025-07-01","to_date":"2025-07-31","status":"Draft"}', 'ignore_prepared_report': 'false', 'are_default_filters': 'true', 'cmd': 'frappe.desk.query_report.run'}
2025-07-20 18:00:24,173 ERROR frappe Error while inserting deferred Error Log record: Error Log bah9o1q2lv: 'Title' (('Line 93: IndentationError: expected an indented block after \'if\' statement on line 92 at statement: \'query = query.where(SalarySlip.payroll_entry == filters.get("payroll_entry"))\'',)) will get truncated, as max characters allowed is 140
Site: rubis
Form Dict: {}
2025-07-20 18:06:01,630 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-07-20 18:06:54,567 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-07-20 23:45:34,328 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'doctype': 'User', 'filters': '{"roles.role":"Rubis Technical Director","enabled":1}', 'fields': '["name"]', 'cmd': 'frappe.client.get_list'}
2025-07-21 00:27:39,378 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'recipient': '<EMAIL>', 'subject': 'Payroll Entry HR-PRUN-2025-00018 Approved', 'message': '\n                                <p>Good Day,</p>\n                                <p>Payroll Entry <b>HR-PRUN-2025-00018</b> has been GM approved.</p>\n                                <p>Click below to review:</p>\n                                <a href="http://127.0.0.1:8003/app/query-report/RTS%20Salary%20Approval?payroll_entry=HR-PRUN-2025-00018" \n                                   style="background: #7575ff; color: white; padding: 8px 12px; border-radius: 4px; text-decoration: none;">\n                                   View Salary Slips\n                                </a>\n                            ', 'reference_doctype': 'Payroll Entry', 'reference_name': 'HR-PRUN-2025-00018', 'cmd': 'gm-notification'}
2025-07-21 00:28:08,480 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'recipient': '<EMAIL>', 'subject': 'Payroll Entry HR-PRUN-2025-00018 Approved', 'message': '\n                                <p>Good Day,</p>\n                                <p>Payroll Entry <b>HR-PRUN-2025-00018</b> has been GM approved.</p>\n                                <p>Click below to review:</p>\n                                <a href="http://127.0.0.1:8003/app/query-report/RTS%20Salary%20Approval?payroll_entry=HR-PRUN-2025-00018" \n                                   style="background: #7575ff; color: white; padding: 8px 12px; border-radius: 4px; text-decoration: none;">\n                                   View Salary Slips\n                                </a>\n                            ', 'reference_doctype': 'Payroll Entry', 'reference_name': 'HR-PRUN-2025-00018', 'cmd': 'gm-notification'}
2025-07-21 00:31:19,368 ERROR frappe New Exception collected in error log
Site: rubis
Form Dict: {'recipient': '<EMAIL>', 'subject': 'Payroll Entry HR-PRUN-2025-00018 Approved', 'message': '\n                                <p>Good Day,</p>\n                                <p>Payroll Entry <b>HR-PRUN-2025-00018</b> has been GM approved.</p>\n                                <p>Click below to review:</p>\n                                <a href="http://127.0.0.1:8003/app/query-report/RTS%20Salary%20Approval?payroll_entry=HR-PRUN-2025-00018" \n                                   style="background: #7575ff; color: white; padding: 8px 12px; border-radius: 4px; text-decoration: none;">\n                                   View Salary Slips\n                                </a>\n                            ', 'reference_doctype': 'Payroll Entry', 'reference_name': 'HR-PRUN-2025-00018', 'cmd': 'gm-notification'}
