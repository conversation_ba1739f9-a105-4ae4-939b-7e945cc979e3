// SOLUTION: Create a Server Script first, then use this Client Script

// Step 1: Create a Server Script with the following code:
/*
Server Script Name: Get Users With Role
Script Type: API
Method Name: get_users_with_role

import frappe

@frappe.whitelist()
def get_users_with_role(role):
    """Get users with specific role - accessible from client side"""
    users = frappe.db.sql("""
        SELECT DISTINCT u.name, u.email
        FROM `tabUser` u
        INNER JOIN `tabHas Role` hr ON hr.parent = u.name
        WHERE hr.role = %s
        AND u.enabled = 1
        AND u.name NOT IN ('Administrator', 'Guest')
        AND hr.parenttype = 'User'
    """, (role,), as_dict=True)

    return [user.email or user.name for user in users]
*/

// Step 2: Use this Client Script:
frappe.ui.form.on('Payroll Entry', {
    refresh: function(frm) {
        if (frm.doc.docstatus === 1) {
            frm.add_custom_button(__('Reviewed by GM'), function () {
                frappe.confirm(__('Send approval notification to <PERSON><PERSON><PERSON> Technical Director?'), function () {
                    // Call the custom server script method
                    frappe.call({
                        method: "frappe.core.doctype.server_script.server_script.get_users_with_role",
                        args: {
                            role: "Rubis Technical Director"
                        },
                        callback: function (res) {
                            if (res.message && res.message.length > 0) {
                                const users = res.message.join(',');

                                const subject = `Payroll Entry ${frm.doc.name} Submitted for Approval`;
                                const link = `${window.location.origin}/app/query-report/RTS%20Salary%20Approval?payroll_entry=${frm.doc.name}`;
                                const message = `
                                    <p>Good Day,</p>
                                    <p>Payroll Entry <b>${frm.doc.name}</b> has been submitted for your approval.</p>
                                    <p>Click below to review and approve the salary slips:</p><br>
                                    <a href="${link}" target="_blank"
                                       style="background-color: #007bff; color: white; padding: 10px 16px; border-radius: 6px; text-decoration: none;">
                                       View Salary Slips
                                    </a>
                                `;

                                // Send the email
                                frappe.call({
                                    method: "frappe.sendmail",
                                    args: {
                                        recipients: users,
                                        subject: subject,
                                        message: message,
                                        reference_doctype: frm.doc.doctype,
                                        reference_name: frm.doc.name
                                    },
                                    callback: function (r) {
                                        if (!r.exc) {
                                            frappe.show_alert(__('Email sent to Rubis Technical Director(s)'), 5);
                                        }
                                    }
                                });

                            } else {
                                frappe.msgprint(__('No users found with the Rubis Technical Director role.'));
                            }
                        }
                    });
                });
            }).addClass('btn-primary');
        }
    }
});
